'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardFooter } from '@/components/ui/card';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from '@/components/ui/pagination';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { OrderFilters, OrdersTable } from '@/components/orders';
import {
  useOrders,
  useSynchronizeOrders,
  useSynchronizeOrdersMultiShop,
  useExportOrders,
  useTikTokShops
} from '@/lib/hooks';
import { useOrdersStore } from '@/lib/store/orders-store';
import { Order, OrderSyncDto, OrderSyncMultiShopDto } from '@/types/order';
import {
  RefreshCw,
  Download,
  ChevronsLeft,
  ChevronsRight,
  RotateCcw
} from 'lucide-react';

export default function TikTokOrdersPage() {
  const router = useRouter();
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [selectedShopForSync, setSelectedShopForSync] = useState<number | null>(null);

  // Use Zustand store for persistent state
  const {
    filters,
    setFilters,
    resetFilters,
    selectedOrderIds,
    toggleSelectAll,
    addSelectedOrderId,
    removeSelectedOrderId,
    clearSelectedOrderIds,
    isFiltersPanelOpen,
    setFiltersPanelOpen,
    lastSyncTime,
    setLastSyncTime
  } = useOrdersStore();

  // Fetch orders with filters
  const {
    data: ordersData,
    isLoading: isLoadingOrders
  } = useOrders(filters);

  // Fetch TikTok shops for sync dropdown
  const { data: shopsData } = useTikTokShops({ limit: 100 });
  const shops = shopsData?.data || [];

  // Mutations
  const {
    mutate: synchronizeOrders,
    isPending: isSynchronizing
  } = useSynchronizeOrders();

  const {
    mutate: synchronizeOrdersMultiShop,
    isPending: isSynchronizingMultiShop
  } = useSynchronizeOrdersMultiShop();

  const {
    mutate: exportOrders,
    isPending: isExporting
  } = useExportOrders();

  // Handle sort change
  const handleSortChange = (value: string) => {
    const [sortField, sortDirection] = value.split('-');
    setFilters({
      sortField,
      sortDirection: sortDirection as 'asc' | 'desc',
      page: 1, // Reset to first page on sort change
    });
  };

  // Handle table sorting
  const handleTableSort = (field: string) => {
    const newDirection =
      filters.sortField === field && filters.sortDirection === 'asc'
        ? 'desc'
        : 'asc';

    setFilters({
      sortField: field,
      sortDirection: newDirection,
      page: 1,
    });
  };

  // Handle select all orders
  const handleSelectAll = (selected: boolean) => {
    const allOrderIds = ordersData?.data.map((order: Order) => order.id) || [];
    toggleSelectAll(allOrderIds, selected);
  };

  // Handle select single order
  const handleSelectOrder = (orderId: number, selected: boolean) => {
    if (selected) {
      addSelectedOrderId(orderId);
    } else {
      removeSelectedOrderId(orderId);
    }
  };

  // Handle view order details
  const handleViewDetails = (order: Order) => {
    router.push(`/client/tiktok/order/${order.id}`);
  };

  // Handle sync orders
  const handleSyncOrders = () => {
    if (!selectedShopForSync) return;

    const syncData: OrderSyncDto = {
      tiktokShopId: selectedShopForSync,
      pageSize: 50,
      sortOrder: 'DESC',
      sortField: 'create_time'
    };

    synchronizeOrders(syncData, {
      onSuccess: () => {
        setIsSyncDialogOpen(false);
        setSelectedShopForSync(null);
        setLastSyncTime(new Date());
        clearSelectedOrderIds();
      }
    });
  };

  // Handle sync all shops
  const handleSyncAllShops = () => {
    if (shops.length === 0) return;

    const allShopIds = shops.map(shop => shop.id);
    const syncData: OrderSyncMultiShopDto = {
      tiktokShopIds: allShopIds,
      pageSize: 50,
      sortOrder: 'DESC',
      sortField: 'create_time',
      maxPages: 10,
      forceFullSync: false
    };

    synchronizeOrdersMultiShop(syncData, {
      onSuccess: (result) => {
        setIsSyncDialogOpen(false);
        setSelectedShopForSync(null);
        setLastSyncTime(new Date());
        clearSelectedOrderIds();

        // Show detailed success message with job information
        console.log('Multi-shop sync jobs created:', result);
      }
    });
  };

  // Handle export orders
  const handleExportOrders = () => {
    exportOrders(filters);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">TikTok Orders</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setIsSyncDialogOpen(true)}
            disabled={isSynchronizing}
          >
            {isSynchronizing ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RotateCcw className="mr-2 h-4 w-4" />
            )}
            Sync Orders
          </Button>
          <Button
            variant="outline"
            onClick={handleExportOrders}
            disabled={isExporting}
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Export
          </Button>
          <Select
            value={`${filters.sortField || 'createTimeTT'}-${filters.sortDirection || 'desc'}`}
            onValueChange={handleSortChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createTimeTT-desc">Newest first</SelectItem>
              <SelectItem value="createTimeTT-asc">Oldest first</SelectItem>
              <SelectItem value="idTT-asc">Order ID A-Z</SelectItem>
              <SelectItem value="idTT-desc">Order ID Z-A</SelectItem>
              <SelectItem value="status-asc">Status A-Z</SelectItem>
              <SelectItem value="payment.totalAmount-desc">Highest value</SelectItem>
              <SelectItem value="payment.totalAmount-asc">Lowest value</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Last sync info */}
      {lastSyncTime && (
        <div className="text-sm text-muted-foreground">
          Last synchronized: {lastSyncTime.toLocaleString()}
        </div>
      )}

      {/* Filters Panel */}
      <OrderFilters
        filters={filters}
        onFiltersChange={setFilters}
        onResetFilters={resetFilters}
        isOpen={isFiltersPanelOpen}
        onToggle={() => setFiltersPanelOpen(!isFiltersPanelOpen)}
      />

      {/* Bulk Actions */}
      {selectedOrderIds.length > 0 && (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleExportOrders}
            disabled={isExporting}
          >
            <Download className="mr-2 h-4 w-4" />
            Export Selected ({selectedOrderIds.length})
          </Button>
        </div>
      )}

      {/* Orders Table */}
      <OrdersTable
        orders={ordersData?.data || []}
        isLoading={isLoadingOrders}
        selectedOrderIds={selectedOrderIds}
        onSelectAll={handleSelectAll}
        onSelectOrder={handleSelectOrder}
        onViewDetails={handleViewDetails}
        onSort={handleTableSort}
        sortField={filters.sortField}
        sortDirection={filters.sortDirection}
      />

      {/* Pagination */}
      {ordersData && ordersData.meta.totalItems > 0 && (
        <Card>
          <CardFooter className="flex items-center justify-between border-t p-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Items per page:</span>
              <Select
                value={filters.limit?.toString() || "20"}
                onValueChange={(value) => setFilters({ limit: parseInt(value), page: 1 })}
              >
                <SelectTrigger className="w-[80px] h-8">
                  <SelectValue placeholder="20" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-muted-foreground ml-4">
                Showing {ordersData.data.length} of {ordersData.meta.totalItems} orders
              </span>
            </div>

            <Pagination>
              <PaginationContent>
                {/* First page button */}
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => setFilters({ page: 1 })}
                    disabled={(filters.page || 1) <= 1 || isLoadingOrders}
                  >
                    <ChevronsLeft className="h-4 w-4" />
                    <span className="sr-only">Go to first page</span>
                  </Button>
                </PaginationItem>

                {/* Previous page button */}
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => (filters.page || 1) > 1 ? setFilters({ page: Math.max(1, (filters.page || 1) - 1) }) : undefined}
                    disabled={(filters.page || 1) <= 1 || isLoadingOrders}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m15 18-6-6 6-6" />
                    </svg>
                    <span className="sr-only">Go to previous page</span>
                  </Button>
                </PaginationItem>

                {/* Current page indicator */}
                <PaginationItem>
                  <span className="px-4 py-2">
                    Page {filters.page || 1} of {ordersData.meta.totalPages || 1}
                  </span>
                </PaginationItem>

                {/* Next page button */}
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => (filters.page || 1) < (ordersData.meta.totalPages || 1) ? setFilters({ page: (filters.page || 1) + 1 }) : undefined}
                    disabled={(filters.page || 1) >= (ordersData.meta.totalPages || 1) || isLoadingOrders}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m9 18 6-6-6-6" />
                    </svg>
                    <span className="sr-only">Go to next page</span>
                  </Button>
                </PaginationItem>

                {/* Last page button */}
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => setFilters({ page: ordersData.meta.totalPages })}
                    disabled={(filters.page || 1) >= (ordersData.meta.totalPages || 1) || isLoadingOrders}
                  >
                    <ChevronsRight className="h-4 w-4" />
                    <span className="sr-only">Go to last page</span>
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </CardFooter>
        </Card>
      )}

      {/* Sync Orders Dialog */}
      <Dialog open={isSyncDialogOpen} onOpenChange={setIsSyncDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Synchronize Orders</DialogTitle>
            <DialogDescription>
              Select a TikTok shop to synchronize orders from, or sync all shops at once. This will fetch the latest orders from TikTok Shop API.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">TikTok Shop</label>
              <Select
                value={selectedShopForSync?.toString() || ''}
                onValueChange={(value) => setSelectedShopForSync(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a shop to sync" />
                </SelectTrigger>
                <SelectContent>
                  {shops.map((shop) => (
                    <SelectItem key={shop.id} value={shop.id.toString()}>
                      {shop.friendly_name || shop.name} ({shop.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {shops.length > 1 && (
              <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>Multi-Shop Sync:</strong> You can sync all {shops.length} shops at once using the "Sync All Shops" button.
                  This will create separate jobs for each shop and process them in parallel.
                </p>
              </div>
            )}
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsSyncDialogOpen(false)}>
              Cancel
            </Button>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                onClick={handleSyncAllShops}
                disabled={shops.length === 0 || isSynchronizing || isSynchronizingMultiShop}
              >
                {isSynchronizingMultiShop ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Syncing All...
                  </>
                ) : (
                  <>
                    <RotateCcw className="mr-2 h-4 w-4" />
                    Sync All Shops ({shops.length})
                  </>
                )}
              </Button>
              <Button
                onClick={handleSyncOrders}
                disabled={!selectedShopForSync || isSynchronizing || isSynchronizingMultiShop}
              >
                {isSynchronizing ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Synchronizing...
                  </>
                ) : (
                  <>
                    <RotateCcw className="mr-2 h-4 w-4" />
                    Sync Selected Shop
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
