import { PaginationQuery } from './pagination';
import { TikTokShopClean } from './shop';

// Order status enum matching backend
export enum OrderStatus {
  UNPAID = 'UNPAID',
  AWAITING_SHIPMENT = 'AWAITING_SHIPMENT',
  AWAITING_COLLECTION = 'AWAITING_COLLECTION',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// Payment info interface for JSONB field
export interface PaymentInfo {
  totalAmount?: string;
  subTotal?: string;
  currency?: string;
  originalTotalProductPrice?: string;
  tax?: string;
  shippingFee?: string;
  platformDiscount?: string;
  sellerDiscount?: string;
  shippingFeeTax?: string;
  productTax?: string;
  retailDeliveryFee?: string;
  buyerServiceFee?: string;
  handlingFee?: string;
  itemInsuranceFee?: string;
  shippingInsuranceFee?: string;
  smallOrderFee?: string;
  originalShippingFee?: string;
  shippingFeePlatformDiscount?: string;
  shippingFeeSellerDiscount?: string;
  shippingFeeCofundedDiscount?: string;
}

// Recipient address interface for JSONB field
export interface RecipientAddress {
  name?: string;
  phone?: string;
  email?: string;
  addressLine1?: string;
  addressLine2?: string;
  addressLine3?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  region?: string;
  fullAddress?: string;
}

// Order line item interface
export interface OrderLineItem {
  id: number;
  idTT?: string; // TikTok line item ID
  productIdTT?: string; // TikTok product ID
  skuIdTT?: string; // TikTok SKU ID
  sellerSku?: string;
  productName?: string;
  skuName?: string;
  skuImage?: string;
  originalPrice?: number;
  salePrice?: number;
  currency?: string;
  quantity?: number; // Added quantity field
  platformDiscount?: number;
  sellerDiscount?: number;
  packageId?: string;
  packageStatus?: string;
  displayStatus?: string;
  isDangerousGood?: boolean;
  isGift?: boolean;
  itemTax?: any[];
  combinedListingSkus?: any[];
  rawTikTokResponse?: any;
  orderId: number;
  createdAt: string | Date;
  updatedAt: string | Date;
}

// Main order interface
export interface Order {
  id: number;
  idTT?: string; // TikTok Shop order ID
  status: OrderStatus;
  orderType?: string;
  createTimeTT?: number; // TikTok timestamp
  updateTimeTT?: number; // TikTok timestamp
  paidTime?: number;
  buyerEmail?: string;
  buyerMessage?: string;
  userIdTT?: string; // TikTok buyer user ID
  payment?: PaymentInfo;
  paymentMethodName?: string;
  recipientAddress?: RecipientAddress;
  fulfillmentType?: string;
  shippingProvider?: string;
  shippingProviderId?: string;
  shippingType?: string;
  trackingNumber?: string;
  deliveryOptionId?: string;
  deliveryOptionName?: string;
  isCod?: boolean;
  isExchangeOrder?: boolean;
  isReplacementOrder?: boolean;
  isSampleOrder?: boolean;
  rawTikTokResponse?: any;
  lineItems?: OrderLineItem[];
  tiktokShopId: number;
  tiktokShop?: TikTokShopClean;
  userId?: number;
  createdAt: string | Date;
  updatedAt: string | Date;
}

// Order filters for querying
export interface OrderFilters extends PaginationQuery {
  // Search filters
  idTT?: string; // Order ID search
  buyerEmail?: string;
  trackingNumber?: string;
  
  // Status filters
  status?: OrderStatus | OrderStatus[];
  
  // TikTok Shop filter
  tiktokShopId?: number;
  
  // Date range filters
  createTimeFrom?: string; // ISO date string
  createTimeTo?: string; // ISO date string
  paidTimeFrom?: string; // ISO date string
  paidTimeTo?: string; // ISO date string
  
  // Order type filters
  orderType?: string;
  fulfillmentType?: string;
  
  // Boolean filters
  isCod?: boolean;
  isExchangeOrder?: boolean;
  isReplacementOrder?: boolean;
  isSampleOrder?: boolean;
  
  // Sorting
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}

// Order query DTO for API requests
export interface OrderQueryDto extends OrderFilters {
  // Inherits all filters from OrderFilters
}

// Create order DTO (if needed for future functionality)
export interface CreateOrderDto {
  idTT: string;
  status: OrderStatus;
  orderType?: string;
  createTimeTT?: number;
  updateTimeTT?: number;
  paidTime?: number;
  buyerEmail?: string;
  buyerMessage?: string;
  userIdTT?: string;
  payment?: PaymentInfo;
  paymentMethodName?: string;
  recipientAddress?: RecipientAddress;
  fulfillmentType?: string;
  shippingProvider?: string;
  shippingProviderId?: string;
  shippingType?: string;
  trackingNumber?: string;
  deliveryOptionId?: string;
  deliveryOptionName?: string;
  isCod?: boolean;
  isExchangeOrder?: boolean;
  isReplacementOrder?: boolean;
  isSampleOrder?: boolean;
  rawTikTokResponse?: any;
  tiktokShopId: number;
  userId?: number;
}

// Order sync DTO for synchronization operations
export interface OrderSyncDto {
  tiktokShopId: number;
  pageSize?: number;
  sortOrder?: 'ASC' | 'DESC';
  sortField?: string;
  filters?: {
    status?: OrderStatus[];
    createTimeFrom?: number;
    createTimeTo?: number;
  };
}

// Multi-shop order sync DTO for synchronizing multiple shops
export interface OrderSyncMultiShopDto {
  tiktokShopIds: number[];
  pageSize?: number;
  sortOrder?: 'ASC' | 'DESC';
  sortField?: string;
  statusFilter?: OrderStatus[];
  createTimeFrom?: number;
  createTimeTo?: number;
  updateTimeFrom?: number;
  updateTimeTo?: number;
  maxPages?: number;
  forceFullSync?: boolean;
}

// Multi-shop sync job result
export interface OrderSyncMultiShopJobResult {
  jobId: string | number;
  tiktokShopId: number;
  shopName: string;
  status: string;
}

// Order sync result
export interface OrderSyncResult {
  totalProcessed: number;
  created: number;
  updated: number;
  errors: number;
  errorDetails?: string[];
}
