# Multi-Shop Order Synchronization - Frontend Implementation

## Overview

This document describes the frontend implementation of the multi-shop order synchronization feature that allows users to sync orders from all their TikTok shops simultaneously.

## Implementation Details

### 1. API Service Layer

**File**: `tts-fe-nextjs/src/lib/api/services/order-service.ts`

Added `synchronizeOrdersMultiShop` function:
```typescript
export const synchronizeOrdersMultiShop = async (
  data: OrderSyncMultiShopDto,
  token?: string
): Promise<OrderSyncMultiShopJobResult[]>
```

### 2. React Query Hook

**File**: `tts-fe-nextjs/src/lib/hooks/use-orders-query.ts`

Added `useSynchronizeOrdersMultiShop` hook:
- Handles API calls to the multi-shop endpoint
- Provides loading states and error handling
- Shows success toast with job information
- Invalidates relevant queries after successful sync

### 3. Updated Order Page

**File**: `tts-fe-nextjs/src/app/(client)/client/tiktok/order/page.tsx`

#### New Features Added:

1. **Multi-Shop Sync Handler**:
   ```typescript
   const handleSyncAllShops = () => {
     const allShopIds = shops.map(shop => shop.id);
     const syncData: OrderSyncMultiShopDto = {
       tiktokShopIds: allShopIds,
       pageSize: 50,
       sortOrder: 'DESC',
       sortField: 'CREATE_TIME',
       maxPages: 10,
       forceFullSync: false
     };
     // ... sync logic
   };
   ```

2. **Enhanced Dialog UI**:
   - Updated dialog description to mention multi-shop option
   - Added informational banner when multiple shops are available
   - New "Sync All Shops" button with shop count display
   - Improved button layout with proper spacing

3. **Loading States**:
   - Separate loading state for multi-shop sync (`isSynchronizingMultiShop`)
   - Buttons disabled during any sync operation
   - Visual feedback with spinning icons

## User Interface Changes

### Dialog Layout

The synchronization dialog now features:

1. **Header**: Updated description mentioning both single and multi-shop options

2. **Shop Selection**: Existing dropdown for single shop selection (unchanged)

3. **Multi-Shop Info Banner**: 
   - Only shown when user has multiple shops
   - Explains the multi-shop functionality
   - Shows total shop count

4. **Button Layout**:
   - **Cancel**: Closes dialog (unchanged)
   - **Sync All Shops**: New secondary button for multi-shop sync
   - **Sync Selected Shop**: Renamed from "Sync Orders" for clarity

### Visual Design

- Uses blue color scheme for the info banner
- Dark mode support for all new elements
- Responsive layout that stacks buttons on mobile
- Clear visual hierarchy with proper spacing

## API Integration

### Request Format

```typescript
POST /orders/synchronize-multi-shop
{
  "tiktokShopIds": [1, 2, 3],
  "pageSize": 50,
  "sortOrder": "DESC",
  "sortField": "CREATE_TIME",
  "maxPages": 10,
  "forceFullSync": false
}
```

### Response Format

```typescript
[
  {
    "jobId": "12345",
    "tiktokShopId": 1,
    "shopName": "My Beauty Store",
    "status": "queued"
  },
  // ... more jobs
]
```

## User Experience

### Success Flow

1. User clicks "Sync All Shops" button
2. Button shows loading state with "Syncing All..." text
3. API creates individual jobs for each shop
4. Success toast shows: "Multi-shop synchronization started! X jobs queued for shops: Shop1, Shop2, ..."
5. Dialog closes and data refreshes
6. Individual jobs can be monitored via existing job status endpoints

### Error Handling

- API errors are handled by the global error interceptor
- Toast notifications show appropriate error messages
- Button states reset on error
- Dialog remains open for user to retry

### Loading States

- **Single Shop Sync**: "Synchronizing..." with spinning icon
- **Multi Shop Sync**: "Syncing All..." with spinning icon
- Both buttons disabled during any sync operation
- Clear visual feedback for user actions

## Benefits

1. **Efficiency**: Sync all shops with one click instead of individual selections
2. **Parallel Processing**: Backend creates separate jobs for concurrent processing
3. **Better UX**: Clear visual distinction between single and multi-shop options
4. **Scalability**: Works with any number of shops
5. **Monitoring**: Individual job tracking for each shop
6. **Backward Compatibility**: Existing single-shop functionality unchanged

## Technical Notes

- Uses existing queue infrastructure and job monitoring
- Maintains consistency with current UI patterns
- Proper TypeScript typing throughout
- Responsive design for mobile devices
- Dark mode support
- Accessibility considerations with proper labeling
