import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ProductQueueService } from './services/product-queue.service';
import { CategoryQueueProcessor } from './processors/category-queue.processor';
import { CategoryQueueService } from './services/category-queue.service';
import { ProductUploadQueueProcessor } from './processors/product-upload-queue.processor';
import { OrderQueueService } from './services/order-queue.service';
import { OrderQueueProcessor } from './processors/order-queue.processor';
import { TikTokShop } from 'src/tiktok-shop/entities/tiktok-shop.entity';
import { Product } from 'src/products/entities/product.entity';
import { ProductImage } from 'src/products/entities/product-image.entity';
import { Sku } from 'src/products/entities/sku.entity';
import { Category } from 'src/products/entities/category.entity';
import { Brand } from 'src/products/entities/brand.entity';
import { Order, OrderLineItem } from 'src/orders/entities';
import { StagedProduct } from 'src/products/entities/staged-product.entity';
import { ProductUpload } from 'src/products/entities/product-upload.entity';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
import { ImageUploadService } from 'src/products/services/image-upload.service';
import { TikTokApplication } from 'src/tiktok-shop/entities/tiktok-application.entity';
import { ProductsService } from 'src/products/products.service';
import { TikTokProductMapper } from 'src/products/mappers/tiktok-product.mapper';
import { TempFileStorageService } from 'src/common/services/temp-file-storage.service';
import { TikTokShopService } from 'src/tiktok-shop/tiktok-shop.service';
import { TikTokOrderMapper } from 'src/orders/mappers';
import { Warehouse } from 'src/tiktok-shop/entities/warehouse.entity';
import { CloudStorageService } from 'src/common/cloud-storage/cloud-storage.service';

@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
        },
      }),
    }),
    BullModule.registerQueue(
      {
        name: 'product-creation',
      },
      {
        name: 'category-sync',
      },
      {
        name: 'order-sync',
      },
    ),
    TypeOrmModule.forFeature([
      TikTokShop,
      Product,
      ProductImage,
      Sku,
      Category,
      Brand,
      StagedProduct,
      ProductUpload,
      TikTokApplication,
      Warehouse,
      Order,
      OrderLineItem,
    ]),
  ],
  providers: [
    ProductQueueService,
    CategoryQueueProcessor,
    CategoryQueueService,
    ProductUploadQueueProcessor,
    OrderQueueService,
    OrderQueueProcessor,
    TikTokClientFactory,
    TikTokProductMapper,
    ImageUploadService,
    ProductsService,
    TempFileStorageService,
    TikTokShopService,
    CloudStorageService,
    TikTokOrderMapper,
  ],
  exports: [BullModule, ProductQueueService, CategoryQueueService, OrderQueueService],
})
export class QueuesModule {}
