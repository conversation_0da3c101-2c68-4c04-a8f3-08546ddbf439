import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { OrderSyncDto, OrderSyncMultiShopDto } from '../../orders/dto/order-sync.dto';
import { TikTokShop } from '../../tiktok-shop/entities/tiktok-shop.entity';

@Injectable()
export class OrderQueueService {
  private readonly logger = new Logger(OrderQueueService.name);

  constructor(
    @InjectQueue('order-sync') private readonly orderQueue: Queue,
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
  ) {}

  /**
   * Add an order synchronization job to the queue
   * @param orderSyncData Order sync data
   * @returns Job ID and status
   */
  async addOrderSyncJob(orderSyncData: {
    syncDto: OrderSyncDto;
    userId: number;
  }) {
    this.logger.log(
      `Adding order synchronization job for TikTok Shop ID: ${orderSyncData.syncDto.tiktokShopId} for user: ${orderSyncData.userId}`,
    );

    const job = await this.orderQueue.add(
      'sync-orders',
      orderSyncData,
      {
        attempts: 3, // Number of retry attempts if job fails
        backoff: {
          type: 'exponential',
          delay: 5000, // 5 seconds initial delay
        },
        removeOnComplete: false, // Keep completed jobs in the queue for monitoring
        removeOnFail: false, // Keep failed jobs in the queue for debugging
      },
    );

    this.logger.log(`Order synchronization job added with ID: ${job.id}`);

    return {
      jobId: job.id,
      status: 'queued',
    };
  }

  /**
   * Add multiple order synchronization jobs to the queue (one per shop)
   * @param multiShopSyncData Multi-shop sync data
   * @returns Array of job IDs and statuses
   */
  async addOrderSyncMultiShopJobs(multiShopSyncData: {
    syncDto: OrderSyncMultiShopDto;
    userId: number;
  }) {
    const { syncDto, userId } = multiShopSyncData;

    this.logger.log(
      `Adding order synchronization jobs for ${syncDto.tiktokShopIds.length} TikTok Shops for user: ${userId}`,
    );

    // Fetch shop information to get friendly names
    const shops = await this.tikTokShopRepository.find({
      where: {
        id: In(syncDto.tiktokShopIds),
        userId
      },
      select: ['id', 'name', 'friendly_name'],
    });

    // Verify all shops exist and belong to the user
    const foundShopIds = shops.map(shop => shop.id);
    const missingShopIds = syncDto.tiktokShopIds.filter(id => !foundShopIds.includes(id));

    if (missingShopIds.length > 0) {
      throw new NotFoundException(
        `TikTok Shops with IDs [${missingShopIds.join(', ')}] not found or access denied`,
      );
    }

    // Create individual sync jobs for each shop
    const jobResults: Array<{
      jobId: string | number;
      tiktokShopId: number;
      shopName: string;
      status: string;
    }> = [];

    for (const shop of shops) {
      // Create individual OrderSyncDto for each shop
      const individualSyncDto: OrderSyncDto = {
        tiktokShopId: shop.id,
        pageSize: syncDto.pageSize,
        sortOrder: syncDto.sortOrder,
        sortField: syncDto.sortField,
        statusFilter: syncDto.statusFilter,
        createTimeFrom: syncDto.createTimeFrom,
        createTimeTo: syncDto.createTimeTo,
        updateTimeFrom: syncDto.updateTimeFrom,
        updateTimeTo: syncDto.updateTimeTo,
        maxPages: syncDto.maxPages,
        forceFullSync: syncDto.forceFullSync,
      };

      // Add job with enhanced data including shop friendly name
      const job = await this.orderQueue.add(
        'sync-orders',
        {
          syncDto: individualSyncDto,
          userId,
          shopFriendlyName: shop.friendly_name || shop.name, // Use friendly name or fallback to name
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: false,
          removeOnFail: false,
        },
      );

      jobResults.push({
        jobId: job.id,
        tiktokShopId: shop.id,
        shopName: shop.friendly_name || shop.name,
        status: 'queued',
      });

      this.logger.log(
        `Order synchronization job added with ID: ${job.id} for shop: ${shop.friendly_name || shop.name} (ID: ${shop.id})`,
      );
    }

    return jobResults;
  }

  /**
   * Get the status of an order sync job
   * @param jobId Job ID
   * @returns Job status information
   */
  async getJobStatus(jobId: string) {
    this.logger.log(`Getting status for order sync job ID: ${jobId}`);

    const job = await this.orderQueue.getJob(jobId);

    if (!job) {
      this.logger.warn(`Job with ID ${jobId} not found`);
      return {
        exists: false,
        message: 'Job not found',
      };
    }

    const state = await job.getState();
    // Get job progress
    let progress = 0;
    try {
      // In Bull, progress is a method that returns the current progress when called with no arguments
      progress = await job.progress();
    } catch (error) {
      this.logger.warn(`Error getting job progress: ${error.message}`);
    }

    return {
      exists: true,
      id: job.id,
      state,
      progress,
      data: job.data,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      stacktrace: job.stacktrace,
      timestamp: job.timestamp,
      finishedOn: job.finishedOn,
    };
  }
}
