import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { OrderSyncDto } from '../../orders/dto/order-sync.dto';

@Injectable()
export class OrderQueueService {
  private readonly logger = new Logger(OrderQueueService.name);

  constructor(
    @InjectQueue('order-sync') private readonly orderQueue: Queue,
  ) {}

  /**
   * Add an order synchronization job to the queue
   * @param orderSyncData Order sync data
   * @returns Job ID and status
   */
  async addOrderSyncJob(orderSyncData: {
    syncDto: OrderSyncDto;
    userId: number;
  }) {
    this.logger.log(
      `Adding order synchronization job for TikTok Shop ID: ${orderSyncData.syncDto.tiktokShopId} for user: ${orderSyncData.userId}`,
    );

    const job = await this.orderQueue.add(
      'sync-orders',
      orderSyncData,
      {
        attempts: 3, // Number of retry attempts if job fails
        backoff: {
          type: 'exponential',
          delay: 5000, // 5 seconds initial delay
        },
        removeOnComplete: false, // Keep completed jobs in the queue for monitoring
        removeOnFail: false, // Keep failed jobs in the queue for debugging
      },
    );

    this.logger.log(`Order synchronization job added with ID: ${job.id}`);

    return {
      jobId: job.id,
      status: 'queued',
    };
  }

  /**
   * Get the status of an order sync job
   * @param jobId Job ID
   * @returns Job status information
   */
  async getJobStatus(jobId: string) {
    this.logger.log(`Getting status for order sync job ID: ${jobId}`);

    const job = await this.orderQueue.getJob(jobId);

    if (!job) {
      this.logger.warn(`Job with ID ${jobId} not found`);
      return {
        exists: false,
        message: 'Job not found',
      };
    }

    const state = await job.getState();
    // Get job progress
    let progress = 0;
    try {
      // In Bull, progress is a method that returns the current progress when called with no arguments
      progress = await job.progress();
    } catch (error) {
      this.logger.warn(`Error getting job progress: ${error.message}`);
    }

    return {
      exists: true,
      id: job.id,
      state,
      progress,
      data: job.data,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      stacktrace: job.stacktrace,
      timestamp: job.timestamp,
      finishedOn: job.finishedOn,
    };
  }
}
